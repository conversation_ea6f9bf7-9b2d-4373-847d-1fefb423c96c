from eyelinkio import read_edf

fname = "data/20250711_110857_m/m.edf"
edf_file = read_edf(fname)
print(edf_file)

# df  = edf_file.to_pandas()               # gaze_x, gaze_y, pupil, status…
# df.head()
# import pyedfread
# samples, events, messages = pyedfread.read_edf('data/20250710_190102_1/1.edf')
# print(samples)
# print(events)
# print(messages)

# from ParseEyeLinkAscFiles.ParseEyeLinkAsc import ParseEyeLinkAsc

# df_rec, df_msg, df_fix, df_sacc, df_blink, df_samples = ParseEyeLinkAsc('data/20250710_115251_gyd/gyd.asc')